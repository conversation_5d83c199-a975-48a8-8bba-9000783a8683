<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class TransactionController extends Controller
{
    // STEP 2: Create a controller
    public function listVoucherTracker(Request $request)
    {
        return $this->workflow('workflow:ListVoucherTracker', $request->all());
    }

    public function viewVoucherReciept(Request $request, $id)
    {
        $request->merge(['id' => $id]);
        return $this->workflow('workflow:ViewVoucherReciept', $request->all());
    }

    public function deleteVoucher(Request $request, $id)
    {
        $request->merge(['id' => $id]);
        return $this->workflow('workflow:deleteVoucher', $request->all());
    }
    public function listTransaction(Request $request, $id, $month = 0, $year = 0)
    {
        if (!is_null($request->query('yearly'))) {
            $request = $request->merge([
                'id' => $id,
                "yearly" => $request->query('yearly'),
                'monthly_transaction' => false
            ]);
        } else {
            $request = $request->merge([
                'id' => $id,
                'month' => $month,
                'year' => $year,
                'monthly_transaction' => true
            ]);
        }
        return $this->workflow('workflow:listTransactionMonthly', $request->all());
    }
    public function listTransactionYearly(Request $request, $id, $month = 0, $year = 0)
    {
        if (!is_null($request->query('yearly'))) {
            $request = $request->merge([
                'id' => $id,
                "yearly" => $request->query('yearly'),
                'monthly_transaction' => false
            ]);
        } else {
            $request = $request->merge([
                'id' => $id,
                'month' => $month,
                'year' => $year,
                'monthly_transaction' => true
            ]);
        }
        return $this->workflow('workflow:listTransactionYearly', $request->all());
    }

    public function downloadTransaction(Request $request, $id,  $month = 0, $year = 0)
    {
        if (!is_null($request->query('yearly'))) {
            $request = $request->merge([
                'id' => $id,
                "yearly" => $request->query('yearly'),
                'monthly_transaction' => false
            ]);
        } else {
            $request = $request->merge([
                'id' => $id,
                'month' => $month,
                'year' => $year,
                'monthly_transaction' => true
            ]);
        }
        return $this->workflow('workflow:downloadTransaction', $request->all());
    }

    public function addDebitNote(Request $request)
    {
        return $this->workflow('workflow:addDebitNote', $request->all());
    }

    public function addMultiplePaymentVoucher(Request $request)
    {
        return $this->workflow('workflow:addMultiplePaymentVoucher', $request->all());
    }

    public function addPaymentVoucher(Request $request)
    {
        return $this->workflow('workflow:addPaymentVoucher', $request->all());
    }

    public function addContraEntry(Request $request)
    {
        return $this->workflow('workflow:addContraEntry', $request->all());
    }

    public function addJournalEntry(Request $request)
    {
        return $this->workflow('workflow:addJournalEntry', $request->all());
    }

    public function accountMongoLog(Request $request)
    {
        return $this->workflow('workflow:accountMongoLog', $request->all());
    }

    public function addTopupInterestTransaction(Request $request, $type = null, $id = null)
    {
        $request = $request->merge([
            'type' => $type,
            'id' => $id
        ]);
        return $this->workflow('workflow:AddTopupInterestTransaction', $request->all());
    }

    public function delete(Request $request, $id, $ledger_id = 0, $month = 0, $year = 0)
    {
        $request = $request->merge([
            'id' => $id,
            'ledger_id' => $ledger_id,
            'month' => $month,
            'year' => $year,
            'monthly_transaction' => true
        ]);
        return $this->workflow('workflow:deleteTransaction', $request->all());
    }

    public function listTransactionDetail(Request $request, $id, $ledger_id = 0, $month = 0, $year = 0)
    {
        $request = $request->merge([
            'id' => $id,
            'ledger_id' => $ledger_id,
            'month' => $month,
            'year' => $year,
            'monthly_transaction' => true
        ]);
        return $this->workflow('workflow:listTransactionDetail', $request->all());
    }

    public function editTransaction(Request $request, $id, $ledger_id = 0, $month = 0, $year = 0)
    {
        $request = $request->merge([
            'id' => $id,
            'ledger_id' => $ledger_id,
            'month' => $month,
            'year' => $year,
            'monthly_transaction' => true
        ]);
        return $this->workflow('workflow:editTransaction', $request->all());
    }


    public function generalSettingsPDF(Request $request, $filename, $label)
    {
        $request = $request->merge([
            'filename' => $filename,
            'label' => $label
        ]);

        return $this->workflow('workflow:generalSettingsPDF', $request->all());
    }

    public function incorrectledgertransaction(Request $request)
    {
        return $this->workflow('workflow:incorrectledgertransaction', $request->all());
    }
}


