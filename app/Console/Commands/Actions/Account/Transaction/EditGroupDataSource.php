<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Carbon\Carbon;
use App\Models\Tenants\ChsoneInvestmentsMaster;
use App\Models\Tenants\ChsoneAssetsCategory;
use App\Models\Tenants\ChsoneTaxCategory;
use App\Models\Tenants\ChsoneTaxClass;
use App\Models\Tenants\ChsoneVendorsMaster;
use App\Models\Tenants\TaxExemption;
use App\Models\Tenants\ChsoneBankRecoMaster;


class EditGroupDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:EditGroup {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Edit Group Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try
        {
            $soc_id = $this->input['company_id'];
            $postedValues = request()->post(); 

            // fecht lstParentAccount details
            $parent_id = $postedValues['lstParentAccount'] ?? $postedValues['parent_id'] ?? '';
            $parentLedger = $this->tenantDB()->table('chsone_grp_ledger_tree')->where('ledger_account_id', $parent_id)->where('soc_id', $soc_id)->first();

            // fetch current financial year detail
            $arrCurrentFYDetailObj = new SocAccountFinancialYearMaster();
            $arrCurrentFYDetail = $arrCurrentFYDetailObj->getFYDetail($soc_id);
            $arrCurrentFYDetail = json_decode(json_encode($arrCurrentFYDetail), true);

            $chsoneGrpLedgerTreeObj = ChsoneGrpLedgerTree::where('soc_id', $soc_id)
                ->where('ledger_account_id', $postedValues['id'])
                ->first();

            if(isset($postedValues['nature']) && strtolower($postedValues['nature']) == 'credit'){
                $postedValues['nature'] = 'cr';
            } else {
                $postedValues['nature'] = 'dr';
            }

            $chsoneGrpLedgerTreeObj->soc_id = $soc_id ?? '';
            $chsoneGrpLedgerTreeObj->ledger_account_name = isset($postedValues['ledger_account_name']) ? $postedValues['ledger_account_name'] : (isset($postedValues['group_account_name']) ? $postedValues['group_account_name'] : '');
            $chsoneGrpLedgerTreeObj->nature_of_account = strtolower($postedValues['nature']) ?? '';
            $chsoneGrpLedgerTreeObj->behaviour = strtolower($postedValues['behaviour']) ?? '';
            $chsoneGrpLedgerTreeObj->parent_id = $postedValues['lstParentAccount'] ?? $postedValues['parent_id'] ?? '';
            $chsoneGrpLedgerTreeObj->status = 1;
            $chsoneGrpLedgerTreeObj->created_by = $this->input['user_id'] ?? '';
            $chsoneGrpLedgerTreeObj->added_on = date('Y-m-d H:i:s');
            $chsoneGrpLedgerTreeObj->report_head = strtolower($parentLedger->report_head) ?? '';
            $chsoneGrpLedgerTreeObj->context = strtolower($parentLedger->context) ?? '';
            $chsoneGrpLedgerTreeObj->behaviour = strtolower($parentLedger->behaviour) ?? '';
            $chsoneGrpLedgerTreeObj->operating_type = strtolower($parentLedger->income_type) ?? '';
            $chsoneGrpLedgerTreeObj->save();

            if($chsoneGrpLedgerTreeObj->save()) {

                $this->message = "Group has been updated successfully";
                $this->status = 'success';
                $this->statusCode = 200;
            } else {
                $this->message = "Unable to update Group";
                $this->status = 'error';
                $this->statusCode = 400;
            }
        }
        catch(\Exception $e){
            $this->message = 'Error: '.$e->getMessage();
            dd($e->getMessage());
        }
    }
}
