<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Masters\ChsoneSocietiesMaster;
use App\Models\Masters\ChsoneSocietiesUser;
use App\Models\Masters\ChsoneUsersMaster;
use App\Models\Tenants\ChsoneUnitsMaster;
use App\Models\Tenants\ChsoneMembersMaster;
use App\Models\Tenants\ChsoneMembersTypeMaster;
use App\Models\Tenants\ChsoneMemberTypeMaster;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;

class CreateAllotteesDataSource extends Action
{
    protected $signature = 'datasource:createAllottees {flowId} {parentId} {input}';
    protected $description = 'Create Allottees';

    public function apply()
    {
        $soc_id  = $this->input['company_id'] ?? '';
        $unit_id = $this->input['fk_unit_id'] ?? '';

        // Retrieve the effective date for the unit.
        $unitEffective = ChsoneUnitsMaster::select('effective_date')
            ->where('soc_id', $soc_id)
            ->where('unit_id', $unit_id)
            ->first();

        // These variables are not used in subsequent code,
        // but kept as per original logic.
        $unit_date   = $unitEffective->effective_date ?? null;
        $member_date = $this->input['effective_date'];

        // Prepare data for saveMember
        $arrListenerdata = [
            'arrPost'                   => $this->input,
            'restrictDuplicatePrimary'  => true,
        ];

        // Save member and return the result.
        $arrListenerdata['arrPost']['member_type_id'] = $this->input['fk_allottee_type_id'] ?? null;
        
        return $this->saveMember($arrListenerdata);
    }

    public function saveMember(array $data)
    {
        $model_err = ['error' => []];
        $arrPost   = $data['arrPost'];
        $soc_id    = $this->input['company_id'] ?? '';
        $unit_id   = $this->input['fk_unit_id'] ?? '';

        // Find the Unit record for the given society and unit id.
        $isAllottedObj = ChsoneUnitsMaster::where('soc_id', $soc_id)
            ->where('unit_id', $arrPost['fk_unit_id'] ?? '')
            ->first();

        // Begin transaction.
        $this->tenantDB()->beginTransaction();

        try {
            // Get the "Primary" member type.
            $primaryMemberType = ChsoneMemberTypeMaster::where('member_type_name', 'Primary')->first();
            if (!$primaryMemberType) {
                $model_err['error'][] = 'Primary member type not found.';
                return $model_err;
            }
            $member_type_id = $primaryMemberType->member_type_id;

            // Count the current primary members.
            $primaryMembersQuery = ChsoneMembersMaster::where('soc_id', $soc_id)
                ->where('fk_unit_id', $arrPost['fk_unit_id'] ?? '')
                ->where('member_type_id', $member_type_id)
                ->where('status', '1')
                ->where('approved', '1');

            $primary_member_cnt = $primaryMembersQuery->count();

            // If updating an existing member.
            if (!empty($arrPost['member_id'])) {
                $previousRecord = ChsoneMembersMaster::where('soc_id', $soc_id)
                    ->where('id', $arrPost['member_id'])
                    ->first();

                if (!$previousRecord) {
                    $model_err['error'][] = 'Previous member record not found.';
                    $this->tenantDB()->rollBack();
                    return $model_err;
                }

                // Find members for the same unit (excluding this member) with a member_type other than Primary.
                $membersWithUnits = ChsoneMembersMaster::where('soc_id', $soc_id)
                    ->where('fk_unit_id', $previousRecord->fk_unit_id ?? '')
                    ->where('member_type_id', '!=', $member_type_id)
                    ->where('id', '!=', $arrPost['member_id'] ?? '')
                    ->where('cancel_date', '0000-00-00')
                    ->get();

                // Adjust the primary member count based on changes.
                if (
                    $previousRecord->member_type_id != $arrPost['member_type_id'] ||
                    $previousRecord->fk_unit_id != $arrPost['fk_unit_id'] ?? ''
                ) {
                    if ($previousRecord->member_type_id == $member_type_id) { // previously primary
                        if ($arrPost['member_type_id'] != $member_type_id) {
                            // Changed to a different member type; count remains unchanged.
                        } else {
                            // If no primary members exist, set count to 0, else increase.
                            $primary_member_cnt = empty($primaryMembersQuery->get()->toArray()) ? 0 : $primary_member_cnt + 1;
                        }
                    }
                } else {
                    if ($arrPost['member_type_id'] == $member_type_id) {
                        $primary_member_cnt = 0;
                    }
                }
            }

            // Check for duplicate primary member if restriction flag is set.
            if (!empty($data['restrictDuplicatePrimary'])) {
                if ($arrPost['member_type_id'] == 1 && $primary_member_cnt > 0) {
                    $model_err["primarymember"] = PRIMARY_MEMBER_ERR_MSG;
                    $model_err['result'] = 0;
                    $this->status = 'error';
                    $this->statusCode = 400;
                    $this->message = PRIMARY_MEMBER_ERR_MSG;
                    $this->tenantDB()->rollBack();
                    return $model_err;
                }
            }

            // Check associate member condition.
            if ($primary_member_cnt <= 0) {
                $model_err["primarymember"] = ASSOCIATE_MEMBER_ERR_MSG;
                $model_err['result'] = 0;
                $this->tenantDB()->rollBack();
                return $model_err;
            }

            // Determine if this is an update or new record.
            if (!empty($arrPost['member_id'])) {
                $memberDetails = ChsoneMembersMaster::where('soc_id', $soc_id)
                    ->where('id', $arrPost['member_id'])
                    ->first();
                $memberDetails->exists = true;

                // Get the old effective date.
                $oldMemberEffective = ChsoneMembersMaster::where('soc_id', $soc_id)
                    ->where('id', $arrPost['member_id'])
                    ->select('effective_date')
                    ->first();
                $old_date = $oldMemberEffective ? $oldMemberEffective->effective_date : null;

                if ($arrPost['effective_date'] == $old_date) {
                    // No effective date change; simply re-use the member ID.
                    $memberDetails->id = $arrPost['member_id'] ?? '';
                } else {
                    // Effective date has changed; create a new instance.
                    $memberDetails = new ChsoneMembersMaster();
                    if (!empty($arrPost['member_id'])) {
                        $memberDetails->id = $arrPost['member_id'] ?? '';
                        $memberDetails->exists = true;
                    }
                    $memberDetails->created_date = Carbon::now()->toDateTimeString();
                    $memberDetails->created_by   = $this->input['user_id'];
                }
            } else {
                // New member.
                $memberDetails = new ChsoneMembersMaster();
                $memberDetails->exists = false;
                $memberDetails->created_date = Carbon::now()->toDateTimeString();
                $memberDetails->created_by   = $this->input['user_id'] ?? '';
            }
            
            // Get the member type ID for "Tenant".
            $tenantType = ChsoneMemberTypeMaster::where('member_type_name', 'Tenant')->first();
            $isTenantId = $tenantType ? $tenantType->member_type_id : null;

            // Update common member fields.
            $memberDetails->soc_id       = $soc_id;
            $memberDetails->updated_date = Carbon::now()->toDateTimeString();
            $memberDetails->updated_by   = $this->input['user_id'] ?? '';
            $memberDetails->effective_date = !empty($arrPost['effective_date'])
                ? $arrPost['effective_date']
                : Carbon::now()->format('Y-m-d');

            $memberDetails->member_first_name    = $arrPost['member_first_name'];
            $memberDetails->member_last_name     = $arrPost['member_last_name'] ?? '';
            $memberDetails->member_email_id      = $arrPost['member_email_id'] ?? ' ';
            $memberDetails->member_mobile_number = $arrPost['member_mobile_number'] ?? ' ';
            $memberDetails->member_intercom      = $arrPost['member_intercom'] ?? '';
            $memberDetails->member_dob           = $arrPost['member_dob'] ?? '';
            $memberDetails->member_gender        = $arrPost['member_gender'] ?? '';
            $memberDetails->member_type_id       = $arrPost['member_type_id'] ?? '';
            $memberDetails->gstin                = $arrPost['gstin'] ?? '';
            $memberDetails->salute               = $arrPost['salute'] ?? '';
            $memberDetails->fk_unit_id           = $arrPost['fk_unit_id'] ?? '';

            // Set approved status.
            if (empty($this->input['user_id'])) {
                $memberDetails->approved = 0;
            } else {
                $memberDetails->approved = !empty($arrPost['member_id'])
                    ? ($previousRecord->approved ?? 1)
                    : 1;
            }

            if (isset($arrPost['user']) && $arrPost['user'] !== 'new_user') {
                $memberDetails->user_id = $this->input['user_id'];
                $memberDetails->is_user_created = 1;
            }

            $memberDetails->is_tenant = (intval($isTenantId) === intval($arrPost['member_type_id'])) ? 1 : 0;

            if (!empty($previousRecord)) {
                $memberDetails->cancel_date = $previousRecord->cancel_date;
                $memberDetails->status      = $previousRecord->status;
            }

            $memberDetails->vizlog_member_id = $arrPost['vizlog_member_id'] ?? 0;
            $memberDetails->unique_code      = $arrPost['unique_code'] ?? '';

            // Attempt to save the member.
            if (!$memberDetails->save()) {
                foreach ($memberDetails->getErrors() as $message) {
                    $model_err['error'][] = $message;
                }
                $model_err['result'] = 0;
                $this->status = 'error';
                $this->statusCode = 400;
                $this->message = json_encode($model_err);
                $this->tenantDB()->rollBack();
                return $model_err;
            } else {
                $this->tenantDB()->commit();
                $this->status = 'success';
                $this->statusCode = 200;
                $this->message = isset($this->input['member_id'])
                    ? 'Member updated successfully.'
                    : 'Member created successfully.';
                return $model_err;
            }

            // Additional unit allotment logic.
            $membersWithUnit = ChsoneMembersMaster::where('soc_id', $soc_id)
                ->where('status', 1)
                ->where('fk_unit_id', $memberDetails->fk_unit_id ?? '')
                ->get();

            $tenantCount = ChsoneMembersMaster::where('soc_id', $soc_id)
                ->where('fk_unit_id', $memberDetails->fk_unit_id ?? '')
                ->where('status', 1)
                ->where('member_type_id', $isTenantId)
                ->where('approved', 1)
                ->count();

            if ($membersWithUnit->count() > 0) {
                if ($tenantCount > 0) {
                    $isAllottedObj->is_allotted = 1;
                    $isAllottedObj->occupied_by = 'tenant';
                } else {
                    if ($memberDetails->is_tenant == 1) {
                        $isAllottedObj->occupied_by = 'tenant';
                    } else {
                        $isAllottedObj->is_allotted = 1;
                        $isAllottedObj->occupied_by = 'owner';
                        $memberDetails->is_tenant = 0;
                    }
                }
            }

            $isAllottedObj->is_occupied = $arrPost['occupied'];
            $isAllottedObj->updated_date = Carbon::now()->toDateTimeString();

            if (empty($previousRecord)) {
                // New record – add.
                if (!$isAllottedObj->save()) {
                    $model_err['error'][] = 'Failed to update unit allotment.';
                    $this->status = 'error';
                    $this->statusCode = 400;
                    $this->message = 'Failed to update unit allotment.';
                    $this->tenantDB()->rollBack();
                    return $model_err;
                }
            } else {
                // Edit – if previous record is active, update.
                if ($previousRecord->status) {
                    if (!$isAllottedObj->save()) {
                        $model_err['error'][] = 'Failed to update unit allotment on edit.';
                        $this->status = 'error';
                        $this->statusCode = 400;
                        $this->message = 'Failed to update unit allotment on edit.';
                        $this->tenantDB()->rollBack();
                        return $model_err;
                    }
                }

                // If a new user is requested.
                if (isset($arrPost['user']) && $arrPost['user'] === 'new_user') {
                    $password = $this->generatePassword();

                    // Determine username based on mobile number if unique.
                    if ($this->uniqueUN(trim($memberDetails->member_mobile_number)) && trim($memberDetails->member_mobile_number) !== '') {
                        $username = trim($memberDetails->member_mobile_number);
                    } else {
                        $username = strtolower(trim($arrPost['member_first_name'])) . substr(md5(now()), 0, 6);
                    }

                    $user = new ChsoneUsersMaster();
                    $user->user_name         = $username;
                    $user->password          = md5($password); // Consider using Hash::make($password) if needed.
                    $user->user_type         = ROLE_MEMBER;
                    $user->role              = ROLE_MEMBER;
                    $user->soc_id            = $soc_id;
                    $user->status            = 1;
                    $user->user_referer      = md5($username);
                    $user->user_dob          = $memberDetails->member_dob ?? ' ';
                    $user->added_on          = Carbon::now()->toDateTimeString();
                    $user->user_first_name   = $arrPost['member_first_name'];
                    $user->user_last_name    = $arrPost['member_last_name'] ?? '';
                    $user->user_email_id     = $arrPost['member_email_id'] ?? ' ';
                    $user->user_mobile_number= $arrPost['member_mobile_number'] ?? ' ';
                    $user->user_lang_iso_639_3 = 'eng';
                    $user->logged_in         = 0;
                    $user->user_gmt_time_zone= date_default_timezone_get();

                    if (!$this->masterDB()->save($user)) {
                        foreach ($user->getErrors() as $message) {
                            $model_err['error'][] = $message;
                        }
                        $model_err['result'] = 0;
                        $this->status = 'error';
                        $this->statusCode = 400;
                        $this->message = json_encode($model_err);
                        $this->tenantDB()->rollBack();
                        return $model_err;
                    } else {
                        // Add user society meta data.
                        $this->addUserSocMeta('', '', [
                            'soc_id'      => $soc_id,
                            'user_id'     => $user->user_id,
                            'user_status' => $user->status,
                        ]);

                        // Update member details with the created user.
                        $memberDetails->user_id         = $user->user_id;
                        $memberDetails->status          = 1;
                        $memberDetails->is_user_created = 1;
                        $memberDetails->cancel_date     = '0000-00-00';
                        $memberDetails->save();

                        // Prepare email data.
                        $data1 = [];
                        $data1['baseurl']      = config('app.full_base_url_fe');
                        $data1['username']     = $username;
                        $data1['support_phone'] = config('mail.support_phone');
                        $data1['support_mail']  = config('mail.support_email');
                        $data1['name']         = $user->user_first_name . " " . $user->user_last_name;
                        $data1['email']        = [$data1['name'] => $user->user_email_id];
                        $data1['code']         = md5($username);
                        $data1['priority']     = 1;
                        $data1['file']         = 'create_user.php';

                        // Get society details.
                        $soc_details = ChsoneSocietiesMaster::where('soc_id', $user->soc_id)
                            ->select('soc_name')
                            ->first();
                        $soc_name = $soc_details->soc_name ?? '';
                        $data1['subject'] = "Welcome to " . $soc_name;
                        $data1['soc_name'] = $soc_name;

                        // Send email.
                        Mail::send('emails.newUser', $data1, function ($message) use ($data1) {
                            $message->to(array_keys($data1['email']), $data1['name'])
                                    ->subject($data1['subject']);
                        });

                        $this->tenantDB()->commit();
                        $this->status = 'success';
                        $this->statusCode = 200;
                        $this->message = 'Member created successfully.';
                        return $model_err;
                    }
                } else {
                    // For non-new-user edits.
                    if (!empty($arrPost['user_id'])) {
                        $memberDetails->user_id = $arrPost['user_id'];
                    }
                    $memberDetails->is_user_created = 1;
                    $this->tenantDB()->commit();
                    $this->status = 'success';
                    $this->statusCode = 200;
                    $this->message = 'Member updated successfully.';
                    return $memberDetails->id;
                }
            }

            // Check additional conditions and return member details if met.
            if (
                (isset($membersWithUnits) && $membersWithUnits->count() >= 1) &&
                ($previousRecord->fk_unit_id != $arrPost['fk_unit_id'] ||
                    ($arrPost['member_type_id'] != $arrPost['member_type_id'] && $arrPost['member_type_id'] != $member_type_id))
            ) {
                $result = [
                    'member_id'    => $arrPost['member_id'],
                    'member_data'  => $membersWithUnits,
                    'member_count' => $membersWithUnits->count(),
                    'result'       => 0
                ];
                $this->tenantDB()->commit();
                return $result;
            } else {
                $this->tenantDB()->commit();
                return true;
            }
        } catch (\Exception $e) {
            dd($e->getLine(), $e->getMessage());
            $this->tenantDB()->rollBack();
            $model_err['error'][] = $e->getMessage();
            $model_err['result'] = 0;
            return $model_err;
        }
    }

    /**
     * Generate a random password.
     *
     * @return string
     */
    private function generatePassword()
    {
        $pw = '';
        $consonants = 'bcdfghjklmnprstvwz';
        $vowels     = 'aeiou';
        // Use two syllables.
        for ($i = 0; $i < 2; $i++) {
            $pw .= $consonants[rand(0, strlen($consonants) - 1)];
            $pw .= $vowels[rand(0, strlen($vowels) - 1)];
        }
        // Add a number.
        $pw .= rand(100, 999);
        return $pw;
    }

    /**
     * Check if a username already exists.
     *
     * @param string $un
     * @return bool
     */
    public function uniqueUN($un)
    {
        return ChsoneUsersMaster::on('master')->where('user_name', $un)->exists();
    }

    /**
     * Add user society meta data.
     *
     * @param mixed $event
     * @param mixed $component
     * @param array $data Contains: soc_id, user_id, user_status, etc.
     * @return bool
     */
    public function addUserSocMeta($event, $component, array $data = [])
    {
        $count = $this->masterDB()
            ->table('chsone_societies_users')
            ->where('soc_id', $data['soc_id'])
            ->where('user_id', $data['user_id'])
            ->count();

        if ($count === 0) {
            $soc_users_meta = new ChsoneSocietiesUser();
            $soc_users_meta->setConnection('master');

            $soc_users_meta->soc_id      = $data['soc_id'];
            $soc_users_meta->user_id     = $data['user_id'];
            $soc_users_meta->user_role   = ROLE_MEMBER;
            $soc_users_meta->user_status = $data['user_status'];

            if ($soc_users_meta->save()) {
                return true;
            } else {
                return false;
            }
        }

        return true;
    }
}
